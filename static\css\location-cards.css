/* تنسيق بطاقات المواقع مع الألوان */

.location-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

/* ألوان البطاقات حسب نوع الموقع */
.location-card[data-type="أمني"] {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 5px solid #2196f3;
}

.location-card[data-type="إداري"] {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    border-left: 5px solid #9c27b0;
}

.location-card[data-type="خدمي"] {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-left: 5px solid #4caf50;
}

.location-card[data-type="تقني"] {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
    border-left: 5px solid #ff9800;
}

.location-card[data-type="طبي"] {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border-left: 5px solid #f44336;
}

.location-card[data-type="تدريبي"] {
    background: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%);
    border-left: 5px solid #009688;
}

/* تأثير التمرير */
.location-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* تنسيق رأس البطاقة */
.location-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.location-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 5px 0;
}

.location-serial {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

/* شارات الحالة */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-maintenance {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-withdrawn {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* معلومات البطاقة */
.location-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #495057;
}

.info-icon {
    margin-left: 8px;
    width: 16px;
    color: #6c757d;
}

/* أيقونات ملونة حسب النوع */
.location-card[data-type="أمني"] .info-icon {
    color: #2196f3;
}

.location-card[data-type="إداري"] .info-icon {
    color: #9c27b0;
}

.location-card[data-type="خدمي"] .info-icon {
    color: #4caf50;
}

.location-card[data-type="تقني"] .info-icon {
    color: #ff9800;
}

.location-card[data-type="طبي"] .info-icon {
    color: #f44336;
}

.location-card[data-type="تدريبي"] .info-icon {
    color: #009688;
}

/* تمييز عدد الأفراد */
.personnel-count {
    font-weight: 600;
    color: #2c3e50;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
}

.personnel-count.has-personnel {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.personnel-count.no-personnel {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* أزرار الإجراءات */
.location-actions {
    display: flex;
    gap: 8px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.location-actions .btn {
    flex: 1;
    font-size: 0.85rem;
    padding: 8px 12px;
}

/* تنسيق الشبكة */
.locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
    .locations-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .location-info {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .location-actions {
        flex-direction: column;
    }
}

/* تأثيرات إضافية */
.location-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.location-card:hover::before {
    opacity: 1;
}

/* تنسيق النص في الوضع المظلم */
body.dark-theme .location-card {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-color: #4a5568;
    color: #e2e8f0;
}

body.dark-theme .location-title {
    color: #f7fafc;
}

body.dark-theme .location-serial {
    color: #a0aec0;
}

body.dark-theme .info-item {
    color: #cbd5e0;
}

/* تنسيق علامات العهد */
.equipment-section {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.equipment-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.equipment-tag {
    background-color: rgba(0, 123, 255, 0.1);
    color: #0056b3;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.75rem;
    border: 1px solid rgba(0, 123, 255, 0.2);
}

/* تنسيق الحالة الفارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
    grid-column: 1 / -1;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #495057;
}

.add-location-btn {
    display: inline-block;
    background-color: #007bff;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.add-location-btn:hover {
    background-color: #0056b3;
    color: white;
    text-decoration: none;
}
