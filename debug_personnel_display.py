#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لتشخيص مشكلة عرض أسماء الأفراد في المواقع
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from locations import get_db_connection, get_location_personnel
from models import Personnel

def debug_personnel_display():
    """تشخيص مشكلة عرض أسماء الأفراد"""
    
    app = create_app()
    
    with app.app_context():
        print("🔍 تشخيص مشكلة عرض أسماء الأفراد في المواقع")
        print("=" * 60)
        
        # الاتصال بقاعدة البيانات
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # البحث عن المواقع التي تحتوي على أفراد
        print("\n📍 البحث عن المواقع التي تحتوي على أفراد:")
        cursor.execute("""
            SELECT l.id, l.name, COUNT(lp.personnel_id) as personnel_count
            FROM locations l
            LEFT JOIN location_personnel lp ON l.id = lp.location_id AND lp.is_active = 1
            GROUP BY l.id, l.name
            HAVING personnel_count > 0
            ORDER BY personnel_count DESC
            LIMIT 5
        """)
        
        locations_with_personnel = cursor.fetchall()
        
        if not locations_with_personnel:
            print("❌ لا توجد مواقع تحتوي على أفراد")
            return
        
        for location in locations_with_personnel:
            location_id, location_name, personnel_count = location
            print(f"\n📍 الموقع: {location_name} (ID: {location_id}) - {personnel_count} فرد")
            
            # فحص بيانات location_personnel
            print("   🔍 فحص بيانات location_personnel:")
            cursor.execute("""
                SELECT personnel_id, shift_type, assignment_date, notes
                FROM location_personnel
                WHERE location_id = ? AND is_active = 1
            """, (location_id,))
            
            location_personnel_records = cursor.fetchall()
            
            for lp in location_personnel_records:
                personnel_id, shift_type, assignment_date, notes = lp
                print(f"     - personnel_id: {personnel_id}")
                print(f"       shift_type: {shift_type}")
                print(f"       notes: {notes}")
                
                # البحث عن الفرد في جدول personnel
                personnel = Personnel.query.filter_by(id=personnel_id).first()
                if personnel:
                    print(f"       ✅ تم العثور على الفرد في جدول personnel:")
                    print(f"          الاسم: {personnel.name}")
                    print(f"          الرقم العسكري: {personnel.personnel_id}")
                    print(f"          رقم الهوية: {personnel.phone}")
                    print(f"          الرتبة: {personnel.rank}")
                else:
                    print(f"       ❌ لم يتم العثور على الفرد في جدول personnel")
                
                print()
            
            # اختبار دالة get_location_personnel
            print("   🧪 اختبار دالة get_location_personnel:")
            personnel_list = get_location_personnel(location_id)
            
            if personnel_list:
                for person in personnel_list:
                    print(f"     - الاسم: {person['name']}")
                    print(f"       الرقم العسكري: {person['personnel_id']}")
                    print(f"       الرتبة: {person['rank']}")
                    print(f"       الحالة: {person['status']}")
                    print()
            else:
                print("     ❌ لا توجد نتائج من دالة get_location_personnel")
            
            print("-" * 50)
        
        # فحص الاستعلام المباشر
        print("\n🔍 فحص الاستعلام المباشر:")
        cursor.execute("""
            SELECT lp.personnel_id, lp.shift_type, lp.assignment_date, lp.notes,
                   p.personnel_id as person_number, p.name, p.rank, p.status, p.phone,
                   w.name as warehouse_name
            FROM location_personnel lp
            LEFT JOIN personnel p ON lp.personnel_id = p.id
            LEFT JOIN warehouses w ON p.warehouse_id = w.id
            WHERE lp.is_active = 1
            LIMIT 10
        """)
        
        direct_results = cursor.fetchall()
        
        for result in direct_results:
            print(f"personnel_id: {result[0]}")
            print(f"name: {result[5]}")
            print(f"person_number: {result[4]}")
            print(f"rank: {result[6]}")
            print("-" * 30)
        
        conn.close()

if __name__ == '__main__':
    debug_personnel_display()
