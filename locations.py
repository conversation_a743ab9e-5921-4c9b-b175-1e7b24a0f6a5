from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, send_file
from flask_login import login_required, current_user
from datetime import datetime
import json
import os
import sqlite3

locations_bp = Blueprint('locations', __name__, url_prefix='/locations')

def get_db_connection():
    """الحصول على اتصال بقاعدة البيانات"""
    conn = sqlite3.connect('locations.db')
    conn.row_factory = sqlite3.Row
    return conn

def init_locations_db():
    """تهيئة جداول المواقع في قاعدة البيانات"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # جدول المواقع الرئيسي
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS locations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            serial_number TEXT UNIQUE NOT NULL,
            type TEXT NOT NULL DEFAULT 'أمني',
            status TEXT NOT NULL DEFAULT 'نشط',
            coordinates TEXT,
            description TEXT,
            instructions_file TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')

    # إضافة عمود ملف التعليمات للجداول الموجودة
    try:
        cursor.execute('ALTER TABLE locations ADD COLUMN instructions_file TEXT')
    except:
        pass  # العمود موجود بالفعل

    # جدول العهد للمواقع
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS location_equipment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            location_id INTEGER NOT NULL,
            equipment_name TEXT NOT NULL,
            equipment_type TEXT,
            serial_number TEXT,
            quantity INTEGER DEFAULT 1,
            condition_status TEXT DEFAULT 'جيد',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE
        )
    ''')

    # جدول ربط الأفراد بالمواقع
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS location_personnel (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            location_id INTEGER NOT NULL,
            personnel_id INTEGER NOT NULL,
            shift_type TEXT DEFAULT 'عام',
            assignment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE,
            FOREIGN KEY (personnel_id) REFERENCES personnel (id) ON DELETE CASCADE,
            UNIQUE(location_id, personnel_id)
        )
    ''')

    conn.commit()
    conn.close()

# تهيئة قاعدة البيانات عند تحميل الوحدة
init_locations_db()

def add_sample_data():
    """إضافة بيانات تجريبية للاختبار"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # التحقق من وجود بيانات
    cursor.execute('SELECT COUNT(*) FROM locations')
    count = cursor.fetchone()[0]

    if count == 0:
        # إضافة مواقع تجريبية
        sample_locations = [
            {
                'name': 'موقع الحراسة الرئيسي',
                'serial_number': 'LOC-001',
                'type': 'أمني',
                'status': 'نشط',
                'coordinates': '24.7136, 46.6753',
                'description': 'موقع الحراسة الرئيسي للمبنى الإداري',
                'equipment': [
                    {'name': 'جهاز لاسلكي موتورولا', 'type': 'جهاز لاسلكي', 'serial': 'MOT-001', 'quantity': 2, 'condition': 'جيد'},
                    {'name': 'قارئ بطاقات', 'type': 'قارئ بطاقات', 'serial': 'CARD-001', 'quantity': 1, 'condition': 'جيد'},
                    {'name': 'كاميرا مراقبة', 'type': 'كاميرا مراقبة', 'serial': 'CAM-001', 'quantity': 4, 'condition': 'جيد'}
                ]
            },
            {
                'name': 'موقع البوابة الشرقية',
                'serial_number': 'LOC-002',
                'type': 'أمني',
                'status': 'نشط',
                'coordinates': '24.7140, 46.6760',
                'description': 'نقطة تفتيش البوابة الشرقية',
                'equipment': [
                    {'name': 'جهاز كشف المعادن', 'type': 'معدات أمنية', 'serial': 'MET-001', 'quantity': 1, 'condition': 'جيد'},
                    {'name': 'جهاز لاسلكي', 'type': 'جهاز لاسلكي', 'serial': 'RAD-002', 'quantity': 1, 'condition': 'متوسط'}
                ]
            },
            {
                'name': 'موقع الصيانة',
                'serial_number': 'LOC-003',
                'type': 'تقني',
                'status': 'تحت الصيانة',
                'coordinates': '24.7130, 46.6745',
                'description': 'ورشة الصيانة والإصلاح',
                'equipment': [
                    {'name': 'أدوات الصيانة', 'type': 'أخرى', 'serial': 'TOOL-001', 'quantity': 1, 'condition': 'يحتاج صيانة'}
                ]
            }
        ]

        for loc_data in sample_locations:
            # إدراج الموقع
            cursor.execute('''
                INSERT INTO locations (name, serial_number, type, status, coordinates, description, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                loc_data['name'],
                loc_data['serial_number'],
                loc_data['type'],
                loc_data['status'],
                loc_data['coordinates'],
                loc_data['description'],
                1  # user_id افتراضي
            ))

            location_id = cursor.lastrowid

            # إدراج العهد
            for equipment in loc_data['equipment']:
                cursor.execute('''
                    INSERT INTO location_equipment
                    (location_id, equipment_name, equipment_type, serial_number, quantity, condition_status)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    location_id,
                    equipment['name'],
                    equipment['type'],
                    equipment['serial'],
                    equipment['quantity'],
                    equipment['condition']
                ))

        conn.commit()
        print("✅ تم إضافة البيانات التجريبية بنجاح")

    conn.close()

# إضافة البيانات التجريبية
add_sample_data()

# بيانات وهمية للمواقع (للاختبار فقط)
locations_data = [
    {
        'id': 1,
        'name': 'الموقع الأول',
        'type': 'أمني',
        'status': 'نشط',
        'personnel_count': 1,
        'coordinates': '24.7136/46.6753',
        'description': 'أدخل وصف الموقع...',
        'basic_info': {
            'location_name': 'اسم الموقع',
            'sms_number': 'SMS رقم',
            'personnel_type': 'نوع الحراس',
            'location_type': 'نوع الموقع'
        },
        'equipment_supplies': {
            'weapon_type': 'نوع السلاح',
            'ammunition_count': 'عدد الذخيرة',
            'notes': 'ملاحظات خاصة'
        },
        'assigned_personnel': {
            'morning_shift': 'لم يحدد الآن',
            'evening_shift': 'لم يحدد الآن'
        },
        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M')
    },
    {
        'id': 2,
        'name': 'الموقع الثاني',
        'type': 'إداري',
        'status': 'نشط',
        'personnel_count': 1,
        'coordinates': '24.7136/46.6753',
        'description': 'أدخل وصف الموقع...',
        'basic_info': {
            'location_name': 'اسم الموقع',
            'sms_number': 'SMS رقم',
            'personnel_type': 'نوع الحراس',
            'location_type': 'نوع الموقع'
        },
        'equipment_supplies': {
            'weapon_type': 'نوع السلاح',
            'ammunition_count': 'عدد الذخيرة',
            'notes': 'ملاحظات خاصة'
        },
        'assigned_personnel': {
            'morning_shift': 'لم يحدد الآن',
            'evening_shift': 'لم يحدد الآن'
        },
        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M')
    }
]

def get_all_locations():
    """جلب جميع المواقع من قاعدة البيانات"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT l.*,
               COUNT(DISTINCT le.id) as equipment_count,
               COUNT(DISTINCT CASE WHEN lp.is_active = 1 THEN lp.personnel_id END) as personnel_count
        FROM locations l
        LEFT JOIN location_equipment le ON l.id = le.location_id
        LEFT JOIN location_personnel lp ON l.id = lp.location_id
        GROUP BY l.id
        ORDER BY l.created_at DESC
    ''')

    locations = []
    for row in cursor.fetchall():
        location = dict(row)

        # جلب العهد للموقع
        cursor.execute('''
            SELECT * FROM location_equipment
            WHERE location_id = ?
            ORDER BY created_at DESC
        ''', (location['id'],))

        equipment = [dict(eq) for eq in cursor.fetchall()]
        location['equipment'] = equipment
        locations.append(location)

    conn.close()
    return locations

def get_location_by_id(location_id):
    """جلب موقع محدد بالمعرف"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM locations WHERE id = ?', (location_id,))
    location_row = cursor.fetchone()

    if not location_row:
        conn.close()
        return None

    location = dict(location_row)

    # جلب العهد للموقع
    cursor.execute('''
        SELECT * FROM location_equipment
        WHERE location_id = ?
        ORDER BY created_at DESC
    ''', (location_id,))

    equipment = [dict(eq) for eq in cursor.fetchall()]
    location['equipment'] = equipment

    # جلب الأفراد المرتبطين بالموقع
    location['personnel'] = get_location_personnel(location_id)

    conn.close()
    return location

def get_location_personnel(location_id):
    """جلب الأفراد المرتبطين بموقع محدد"""
    personnel_list = []

    try:
        # استخدام SQLAlchemy للوصول إلى جدول personnel في قاعدة البيانات الرئيسية
        from models import Personnel

        conn = get_db_connection()
        cursor = conn.cursor()

        # جلب الأفراد المرتبطين بالموقع من location_personnel
        cursor.execute('''
            SELECT personnel_id, shift_type, assignment_date, notes
            FROM location_personnel
            WHERE location_id = ? AND is_active = 1
        ''', (location_id,))

        location_personnel = cursor.fetchall()

        # تحويل البيانات إلى قائمة
        for lp in location_personnel:
            personnel_id = lp['personnel_id']

            # البحث عن الفرد في جدول personnel باستخدام SQLAlchemy
            personnel = Personnel.query.filter_by(id=personnel_id).first()

            if personnel:
                # إذا تم العثور على الفرد في جدول personnel، استخدم بياناته
                personnel_data = {
                    'id': personnel_id,
                    'personnel_id': personnel.personnel_id,
                    'name': personnel.name,
                    'rank': personnel.rank or 'غير محدد',
                    'status': personnel.status or 'نشط',
                    'phone': personnel.phone or '',
                    'warehouse_name': personnel.warehouse.name if personnel.warehouse else 'غير محدد',
                    'shift_type': lp['shift_type'] or 'عام',
                    'assignment_date': lp['assignment_date'],
                    'notes': lp['notes'] or ''
                }
            else:
                # إذا لم يتم العثور على الفرد، حاول استخراج البيانات من الملاحظات أو استخدم بيانات افتراضية
                notes = lp['notes'] or ''
                name = f'فرد رقم {personnel_id}'
                rank = 'غير محدد'

                # محاولة استخراج الاسم والرتبة من الملاحظات
                if 'فرد تجريبي -' in notes:
                    parts = notes.split(' - ')
                    if len(parts) >= 3:
                        name = parts[1].strip()
                        rank = parts[2].strip()

                personnel_data = {
                    'id': personnel_id,
                    'personnel_id': f'M{personnel_id:04d}',
                    'name': name,
                    'rank': rank,
                    'status': 'نشط',
                    'phone': '',
                    'warehouse_name': 'غير محدد',
                    'shift_type': lp['shift_type'] or 'عام',
                    'assignment_date': lp['assignment_date'],
                    'notes': notes
                }

            personnel_list.append(personnel_data)

    except Exception as e:
        print(f"خطأ في جلب الأفراد: {e}")

    return personnel_list

def add_personnel_to_location(location_id, personnel_id, shift_type='عام', notes=''):
    """إضافة فرد إلى موقع"""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # التحقق من وجود الفرد في الموقع الحالي (نشط أو غير نشط)
        cursor.execute('''
            SELECT id, is_active FROM location_personnel
            WHERE location_id = ? AND personnel_id = ?
        ''', (location_id, personnel_id))

        existing_record = cursor.fetchone()

        if existing_record:
            if existing_record['is_active'] == 1:
                conn.close()
                return False, 'هذا الفرد مضاف بالفعل في هذا الموقع'
            else:
                # التحقق من وجود الفرد في مواقع أخرى نشطة
                cursor.execute('''
                    SELECT l.name as location_name FROM location_personnel lp
                    JOIN locations l ON l.id = lp.location_id
                    WHERE lp.personnel_id = ? AND lp.is_active = 1 AND lp.location_id != ?
                ''', (personnel_id, location_id))

                other_location = cursor.fetchone()
                if other_location:
                    conn.close()
                    return False, f'الفرد مرتبط بالفعل بموقع: {other_location["location_name"]}'

                # إعادة تفعيل السجل الموجود
                cursor.execute('''
                    UPDATE location_personnel
                    SET is_active = 1, shift_type = ?, notes = ?,
                        assignment_date = datetime('now'), updated_at = datetime('now')
                    WHERE id = ?
                ''', (shift_type, notes, existing_record['id']))

                conn.commit()
                conn.close()
                return True, 'تم إعادة إضافة الفرد للموقع بنجاح'
        else:
            # التحقق من وجود الفرد في مواقع أخرى نشطة قبل إضافة سجل جديد
            cursor.execute('''
                SELECT l.name as location_name FROM location_personnel lp
                JOIN locations l ON l.id = lp.location_id
                WHERE lp.personnel_id = ? AND lp.is_active = 1
            ''', (personnel_id,))

            other_location = cursor.fetchone()
            if other_location:
                conn.close()
                return False, f'الفرد مرتبط بالفعل بموقع: {other_location["location_name"]}'

            # إضافة سجل جديد
            cursor.execute('''
                INSERT INTO location_personnel (location_id, personnel_id, shift_type, notes, assignment_date, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, datetime('now'), 1, datetime('now'), datetime('now'))
            ''', (location_id, personnel_id, shift_type, notes))

            conn.commit()
            conn.close()
            return True, 'تم إضافة الفرد للموقع بنجاح'

    except Exception as e:
        conn.rollback()
        conn.close()
        return False, f'خطأ في إضافة الفرد: {str(e)}'

def remove_personnel_from_location(location_id, personnel_id):
    """إزالة فرد من موقع"""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # تعطيل الربط بدلاً من الحذف للاحتفاظ بالسجل
        cursor.execute('''
            UPDATE location_personnel
            SET is_active = 0, updated_at = CURRENT_TIMESTAMP
            WHERE location_id = ? AND personnel_id = ?
        ''', (location_id, personnel_id))

        if cursor.rowcount == 0:
            conn.close()
            return False, 'الفرد غير مرتبط بالموقع'

        conn.commit()
        conn.close()
        return True, 'تم إزالة الفرد من الموقع بنجاح'

    except Exception as e:
        conn.rollback()
        conn.close()
        return False, f'خطأ في إزالة الفرد: {str(e)}'

@locations_bp.route('/')
@login_required
def index():
    """صفحة إدارة المواقع الرئيسية"""
    # تأكد من وجود الجداول المطلوبة
    init_locations_db()

    locations = get_all_locations()
    return render_template('locations/index.html', locations=locations)

@locations_bp.route('/add')
@login_required
def add_location():
    """صفحة إضافة موقع جديد"""
    return render_template('locations/add.html')

@locations_bp.route('/create', methods=['POST'])
@login_required
def create_location():
    """إنشاء موقع جديد"""
    try:
        # تحديد نوع البيانات المرسلة
        is_json_request = 'application/json' in request.headers.get('Accept', '')
        data = request.get_json() if request.is_json else request.form

        # التحقق من البيانات المطلوبة
        name = data.get('name', '').strip()
        serial_number = data.get('serial_number', '').strip()

        if not name:
            raise ValueError('اسم الموقع مطلوب')
        if not serial_number:
            raise ValueError('الرقم التسلسلي مطلوب')

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم تكرار الرقم التسلسلي
        cursor.execute('SELECT id FROM locations WHERE serial_number = ?', (serial_number,))
        if cursor.fetchone():
            raise ValueError('الرقم التسلسلي موجود مسبقاً')

        # معالجة رفع ملف التعليمات
        instructions_filename = None
        if 'instructions_file' in request.files:
            file = request.files['instructions_file']
            if file and file.filename and file.filename.lower().endswith('.pdf'):
                # إنشاء مجلد للملفات إذا لم يكن موجوداً
                import os
                upload_folder = os.path.join('static', 'uploads', 'location_instructions')
                os.makedirs(upload_folder, exist_ok=True)

                # إنشاء اسم ملف فريد
                import uuid
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                unique_filename = f"{timestamp}_{uuid.uuid4().hex[:8]}_{file.filename}"
                file_path = os.path.join(upload_folder, unique_filename)

                # حفظ الملف
                file.save(file_path)
                instructions_filename = unique_filename

        # إدراج الموقع الجديد
        cursor.execute('''
            INSERT INTO locations (name, serial_number, type, status, coordinates, description, instructions_file, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            name,
            serial_number,
            data.get('type', 'أمني'),
            data.get('status', 'نشط'),
            data.get('coordinates', ''),
            data.get('description', ''),
            instructions_filename,
            getattr(current_user, 'id', 1)
        ))

        location_id = cursor.lastrowid

        # إضافة العهد إذا تم تحديدها
        equipment_list = data.get('equipment', [])
        if isinstance(equipment_list, str):
            equipment_list = json.loads(equipment_list)

        for equipment in equipment_list:
            if equipment.get('name', '').strip():
                cursor.execute('''
                    INSERT INTO location_equipment
                    (location_id, equipment_name, equipment_type, serial_number, quantity, condition_status, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    location_id,
                    equipment.get('name', ''),
                    equipment.get('type', ''),
                    equipment.get('serial_number', ''),
                    int(equipment.get('quantity', 1)),
                    equipment.get('condition', 'جيد'),
                    equipment.get('notes', '')
                ))

        conn.commit()

        # جلب الموقع المُنشأ
        new_location = get_location_by_id(location_id)
        conn.close()

        if is_json_request:
            return jsonify({'success': True, 'message': 'تم إضافة الموقع بنجاح', 'location': new_location})
        else:
            flash('تم إضافة الموقع بنجاح', 'success')
            return redirect(url_for('locations.index'))

    except Exception as e:
        if is_json_request:
            return jsonify({'success': False, 'message': f'خطأ في إضافة الموقع: {str(e)}'})
        else:
            flash(f'خطأ في إضافة الموقع: {str(e)}', 'error')
            return redirect(url_for('locations.add_location'))

@locations_bp.route('/<int:location_id>')
@login_required
def view_location(location_id):
    """عرض تفاصيل موقع محدد"""
    location = get_location_by_id(location_id)
    if not location:
        flash('الموقع غير موجود', 'error')
        return redirect(url_for('locations.index'))

    return render_template('locations/view.html', location=location)

@locations_bp.route('/<int:location_id>/delete', methods=['POST'])
@login_required
def delete_location_api(location_id):
    """حذف موقع محدد"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود الموقع
        cursor.execute('SELECT name FROM locations WHERE id = ?', (location_id,))
        location = cursor.fetchone()

        if not location:
            return jsonify({'success': False, 'message': 'الموقع غير موجود'})

        # تسجيل معلومات الحذف في السجل أو جدول الأنشطة قبل الحذف
        try:
            # تسجيل معلومات الموقع قبل حذفه
            cursor.execute('SELECT * FROM locations WHERE id = ?', (location_id,))
            location_data = cursor.fetchone()
            
            # حفظ العملية في السجل
            from models import ActivityLog
            from flask import request
            from flask_login import current_user
            log = ActivityLog(
                action="حذف موقع",
                description=f"تم حذف الموقع: {location['name']}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=1  # استخدم المستودع الافتراضي
            )
            from db import db
            db.session.add(log)
            db.session.commit()
        except Exception as e:
            # إذا فشل التسجيل، نستمر في الحذف
            pass

        # حذف الموقع (سيتم حذف العهد تلقائياً بسبب CASCADE)
        cursor.execute('DELETE FROM locations WHERE id = ?', (location_id,))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'تم حذف الموقع بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في حذف الموقع: {str(e)}'})

@locations_bp.route('/<int:location_id>/edit')
@login_required
def edit_location(location_id):
    """صفحة تعديل موقع محدد"""
    location = get_location_by_id(location_id)
    if not location:
        flash('الموقع غير موجود', 'error')
        return redirect(url_for('locations.index'))

    return render_template('locations/edit.html', location=location)

@locations_bp.route('/<int:location_id>/update', methods=['POST'])
@login_required
def update_location(location_id):
    """تحديث موقع محدد"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود الموقع
        cursor.execute('SELECT id FROM locations WHERE id = ?', (location_id,))
        if not cursor.fetchone():
            return jsonify({'success': False, 'message': 'الموقع غير موجود'})

        data = request.form

        # تحديث بيانات الموقع
        cursor.execute('''
            UPDATE locations
            SET name = ?, serial_number = ?, type = ?, status = ?,
                coordinates = ?, description = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (
            data.get('name'),
            data.get('serial_number'),
            data.get('type'),
            data.get('status'),
            data.get('coordinates'),
            data.get('description'),
            location_id
        ))

        # حذف العهد القديمة
        cursor.execute('DELETE FROM location_equipment WHERE location_id = ?', (location_id,))

        # إضافة العهد الجديدة
        if 'equipment' in data:
            equipment_data = json.loads(data['equipment'])
            for equipment in equipment_data:
                if equipment.get('name') and equipment.get('name').strip():
                    cursor.execute('''
                        INSERT INTO location_equipment
                        (location_id, equipment_name, equipment_type, serial_number, quantity, condition_status, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        location_id,
                        equipment.get('name'),
                        equipment.get('type'),
                        equipment.get('serial_number'),
                        equipment.get('quantity', 1),
                        equipment.get('condition'),
                        equipment.get('notes')
                    ))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'تم تحديث الموقع بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في تحديث الموقع: {str(e)}'})

@locations_bp.route('/api/list')
@login_required
def api_list_locations():
    """API لجلب قائمة المواقع للاستخدام في كشف الاستلامات"""
    try:
        locations = get_all_locations()
        simplified_locations = []
        for location in locations:
            simplified_locations.append({
                'id': location['id'],
                'name': location['name'],
                'type': location['type'],
                'description': location.get('description', '')
            })

        return jsonify({
            'success': True,
            'locations': simplified_locations,
            'count': len(simplified_locations)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب المواقع: {str(e)}',
            'locations': []
        })

@locations_bp.route('/<int:location_id>/personnel/search')
@login_required
def search_personnel_for_location(location_id):
    """البحث عن فرد لإضافته للموقع"""
    try:
        search_term = request.args.get('search_term', '').strip()
        print(f"🔍 البحث عن: '{search_term}' في الموقع {location_id}")

        if not search_term:
            print("❌ لا يوجد مصطلح بحث")
            return jsonify({'success': False, 'message': 'رقم الهوية أو الرقم العسكري مطلوب'})

        # التحقق من وجود الموقع
        location = get_location_by_id(location_id)
        if not location:
            return jsonify({'success': False, 'message': 'الموقع غير موجود'})

        # البحث عن الفرد
        from models import Personnel
        from flask_login import current_user

        # البحث في جميع الأفراد - متاح لجميع المستخدمين
        personnel = Personnel.query.filter(
            (Personnel.phone == search_term) | (Personnel.personnel_id == search_term)
        ).first()

        print(f"نتيجة البحث: {personnel.name if personnel else 'لا يوجد'}")

        if not personnel:
            print("❌ لم يتم العثور على فرد")
            return jsonify({'success': False, 'message': 'لم يتم العثور على فرد بهذا الرقم'})

        # التحقق من ارتباط الفرد بالموقع مسبقاً
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id FROM location_personnel
            WHERE location_id = ? AND personnel_id = ? AND is_active = 1
        ''', (location_id, personnel.id))

        already_assigned = cursor.fetchone() is not None
        conn.close()

        response_data = {
            'success': True,
            'personnel': {
                'id': personnel.id,
                'personnel_id': personnel.personnel_id,
                'name': personnel.name,
                'rank': personnel.rank,
                'status': personnel.status,
                'phone': personnel.phone,
                'warehouse_name': personnel.warehouse.name if personnel.warehouse else 'غير محدد'
            },
            'already_assigned': already_assigned,
            'warning_message': 'هذا الفرد مضاف بالفعل في هذا الموقع' if already_assigned else None
        }
        print(f"✅ إرسال استجابة ناجحة: {response_data}")
        return jsonify(response_data)

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في البحث: {str(e)}'})

@locations_bp.route('/<int:location_id>/personnel/add', methods=['POST'])
@login_required
def add_personnel_to_location_api(location_id):
    """إضافة فرد للموقع"""
    try:
        data = request.get_json()
        print(f"📥 طلب إضافة فرد للموقع {location_id}: {data}")

        personnel_id = data.get('personnel_id')
        shift_type = data.get('shift_type', 'عام')
        notes = data.get('notes', '')

        print(f"معرف الفرد: {personnel_id}, نوع الوردية: {shift_type}")

        if not personnel_id:
            return jsonify({'success': False, 'message': 'معرف الفرد مطلوب'})

        # التحقق من وجود الموقع
        location = get_location_by_id(location_id)
        if not location:
            return jsonify({'success': False, 'message': 'الموقع غير موجود'})

        # إضافة الفرد للموقع
        success, message = add_personnel_to_location(location_id, personnel_id, shift_type, notes)

        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إضافة الفرد: {str(e)}'})

@locations_bp.route('/<int:location_id>/personnel/<int:personnel_id>/remove', methods=['POST'])
@login_required
def remove_personnel_from_location_api(location_id, personnel_id):
    """إزالة فرد من الموقع"""
    try:
        # التحقق من وجود الموقع
        location = get_location_by_id(location_id)
        if not location:
            return jsonify({'success': False, 'message': 'الموقع غير موجود'})

        # إزالة الفرد من الموقع
        success, message = remove_personnel_from_location(location_id, personnel_id)

        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إزالة الفرد: {str(e)}'})

@locations_bp.route('/<int:location_id>/personnel/list')
@login_required
def list_location_personnel(location_id):
    """جلب قائمة الأفراد المرتبطين بالموقع"""
    try:
        # التحقق من وجود الموقع
        location = get_location_by_id(location_id)
        if not location:
            return jsonify({'success': False, 'message': 'الموقع غير موجود'})

        personnel_list = get_location_personnel(location_id)

        return jsonify({
            'success': True,
            'personnel': personnel_list,
            'count': len(personnel_list)
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في جلب الأفراد: {str(e)}'})

@locations_bp.route('/fix-personnel-count')
@login_required
def fix_personnel_count():
    """إصلاح مشكلة عدد الأفراد في المواقع"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود جدول personnel
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='personnel'
        """)
        personnel_table_exists = cursor.fetchone() is not None

        deleted_orphaned = 0
        if personnel_table_exists:
            # حذف السجلات المعطلة (أفراد غير موجودين في جدول personnel)
            cursor.execute("""
                DELETE FROM location_personnel
                WHERE personnel_id NOT IN (SELECT id FROM personnel)
            """)
            deleted_orphaned = cursor.rowcount

        # إصلاح التكرارات
        cursor.execute("""
            SELECT location_id, personnel_id, COUNT(*) as count
            FROM location_personnel
            WHERE is_active = 1
            GROUP BY location_id, personnel_id
            HAVING count > 1
        """)

        duplicates = cursor.fetchall()
        deleted_duplicates = 0

        for dup in duplicates:
            # الاحتفاظ بأحدث سجل وحذف الباقي
            cursor.execute("""
                DELETE FROM location_personnel
                WHERE location_id = ? AND personnel_id = ? AND is_active = 1
                AND id NOT IN (
                    SELECT id FROM location_personnel
                    WHERE location_id = ? AND personnel_id = ? AND is_active = 1
                    ORDER BY created_at DESC
                    LIMIT 1
                )
            """, (dup['location_id'], dup['personnel_id'], dup['location_id'], dup['personnel_id']))
            deleted_duplicates += cursor.rowcount

        conn.commit()
        conn.close()

        message = f'تم إصلاح البيانات بنجاح.'
        if personnel_table_exists:
            message += f' حذف {deleted_orphaned} سجل معطل و {deleted_duplicates} سجل مكرر.'
        else:
            message += f' حذف {deleted_duplicates} سجل مكرر. (جدول personnel غير موجود)'

        return jsonify({
            'success': True,
            'message': message,
            'deleted_orphaned': deleted_orphaned,
            'deleted_duplicates': deleted_duplicates,
            'personnel_table_exists': personnel_table_exists
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إصلاح البيانات: {str(e)}'})

@locations_bp.route('/check-tables')
@login_required
def check_tables():
    """فحص الجداول الموجودة في قاعدة البيانات"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # فحص الجداول الموجودة
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table'
            ORDER BY name
        """)
        tables = [row['name'] for row in cursor.fetchall()]

        # فحص جدول location_personnel
        personnel_count = 0
        if 'location_personnel' in tables:
            cursor.execute("SELECT COUNT(*) as count FROM location_personnel WHERE is_active = 1")
            personnel_count = cursor.fetchone()['count']

        # فحص جدول locations
        locations_count = 0
        if 'locations' in tables:
            cursor.execute("SELECT COUNT(*) as count FROM locations")
            locations_count = cursor.fetchone()['count']

        conn.close()

        return jsonify({
            'success': True,
            'tables': tables,
            'personnel_table_exists': 'personnel' in tables,
            'location_personnel_count': personnel_count,
            'locations_count': locations_count,
            'message': f'تم العثور على {len(tables)} جدول في قاعدة البيانات'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في فحص الجداول: {str(e)}'})

@locations_bp.route('/add-test-personnel/<int:location_id>')
@login_required
def add_test_personnel(location_id):
    """إضافة أفراد تجريبيين لموقع للاختبار"""
    try:
        # التحقق من وجود الموقع
        location = get_location_by_id(location_id)
        if not location:
            return jsonify({'success': False, 'message': 'الموقع غير موجود'})

        conn = get_db_connection()
        cursor = conn.cursor()

        # إضافة أفراد تجريبيين مباشرة في جدول location_personnel
        import random
        test_personnel = [
            {'id': 1001, 'name': 'أحمد محمد علي', 'rank': 'عريف', 'shift': 'صباحي'},
            {'id': 1002, 'name': 'محمد أحمد سالم', 'rank': 'جندي أول', 'shift': 'مسائي'},
            {'id': 1003, 'name': 'سالم علي محمد', 'rank': 'رقيب', 'shift': 'ليلي'},
            {'id': 1004, 'name': 'علي سالم أحمد', 'rank': 'جندي', 'shift': 'عام'},
            {'id': 1005, 'name': 'محمود حسن علي', 'rank': 'عريف أول', 'shift': 'صباحي'}
        ]

        added_count = 0
        for person in test_personnel:
            # التحقق من عدم وجود الفرد في الموقع
            cursor.execute('''
                SELECT id FROM location_personnel
                WHERE location_id = ? AND personnel_id = ? AND is_active = 1
            ''', (location_id, person['id']))

            if not cursor.fetchone():
                cursor.execute('''
                    INSERT INTO location_personnel (location_id, personnel_id, shift_type, notes, assignment_date, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, datetime('now'), 1, datetime('now'), datetime('now'))
                ''', (location_id, person['id'], person['shift'], f'فرد تجريبي - {person["name"]} - {person["rank"]}'))
                added_count += 1

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'تم إضافة {added_count} فرد تجريبي للموقع {location["name"]}',
            'added_count': added_count,
            'location_name': location['name']
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إضافة الأفراد التجريبيين: {str(e)}'})

@locations_bp.route('/clear-test-personnel/<int:location_id>')
@login_required
def clear_test_personnel(location_id):
    """حذف جميع الأفراد التجريبيين من موقع"""
    try:
        # التحقق من وجود الموقع
        location = get_location_by_id(location_id)
        if not location:
            return jsonify({'success': False, 'message': 'الموقع غير موجود'})

        conn = get_db_connection()
        cursor = conn.cursor()

        # حذف جميع الأفراد من الموقع
        cursor.execute('''
            DELETE FROM location_personnel
            WHERE location_id = ?
        ''', (location_id,))

        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'تم حذف {deleted_count} فرد من الموقع {location["name"]}',
            'deleted_count': deleted_count,
            'location_name': location['name']
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في حذف الأفراد: {str(e)}'})

@locations_bp.route('/test-personnel')
@login_required
def test_personnel_page():
    """صفحة إدارة الأفراد التجريبيين"""
    return render_template('locations/test_personnel.html')

@locations_bp.route('/<int:location_id>/instructions')
@login_required
def download_instructions(location_id):
    """تحميل ملف تعليمات الموقع"""
    try:
        # الحصول على معلومات الموقع
        location = get_location_by_id(location_id)
        if not location:
            flash('الموقع غير موجود', 'error')
            return redirect(url_for('locations.index'))

        if not location.get('instructions_file'):
            flash('لا يوجد ملف تعليمات لهذا الموقع', 'warning')
            return redirect(url_for('locations.view_location', location_id=location_id))

        # مسار الملف
        file_path = os.path.join('static', 'uploads', 'location_instructions', location['instructions_file'])

        if not os.path.exists(file_path):
            flash('ملف التعليمات غير موجود', 'error')
            return redirect(url_for('locations.view_location', location_id=location_id))

        # إرسال الملف للتحميل
        return send_file(
            file_path,
            as_attachment=True,
            download_name=f"تعليمات_{location['name']}.pdf"
        )

    except Exception as e:
        flash(f'خطأ في تحميل ملف التعليمات: {str(e)}', 'error')
        return redirect(url_for('locations.view_location', location_id=location_id))

